import { AddToCartButton } from '@/libs/products/components/AddToCartForm/components/AddToCartButton/AddToCartButton';
import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import styles from './PromoMatcherModal.module.css';
import { PromoMatcherProduct } from '../PromoMatcherProduct/PromoMatcherProduct';
import { PromoMatcherTitle } from '../PromoMatcherTitle/PromoMatcherTitle';
import { Subtotal } from '../Subtotal/Subtotal';

type PromoMatcherProductsModalOptions = ModalOptionProps & {
  promoType: string;
  title: string;
  products: {
    id: string;
    name: string;
    sku: string;
    vendor: string;
    price: number;
    increments: number;
  }[];
  subtotal: number;
  originalPrice: number;
};

export const PromoMatcherModal = () => {
  const { modalOption } = useModalStore();
  const { promoType, title, products, subtotal, originalPrice } =
    modalOption as PromoMatcherProductsModalOptions;

  if (!products) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.PROMO_MATCHER_PRODUCTS}
      size="auto"
      withCloseButton
      customClasses={{ header: styles.modalHeader, body: styles.modalBody }}
    >
      <div className="flex flex-col">
        <div className="flex-col bg-white p-6 pt-0">
          <h3 className="mb-2 text-2xl font-medium">
            You&apos;re Almost There!
          </h3>
          <p className="mb-6 text-sm text-black/65">
            Follow the steps below to claim your savings before this offer
            expires.
          </p>
          <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
            <PromoMatcherTitle promoType={promoType} title={title} />
            <div className="divider-h"></div>
            <div className="grid gap-2">
              {products.map((product) => (
                <PromoMatcherProduct key={product.id} product={product} />
              ))}
            </div>
            <p className="mr-2 mb-4 inline-block text-sm">
              Buy now and get <strong>9 for free!</strong>
            </p>
            <Button className={styles.seeList} variant="unstyled">
              See List
            </Button>
            <div className="divider-h"></div>
            <div className="flex items-center justify-between">
              <Subtotal
                subtotal={subtotal}
                originalPrice={originalPrice}
                itemsCount={products.length}
              />
              <div className="w-52">
                <AddToCartButton isLoading={false} fullWidth />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { getPriceString } from '@/utils';

type PromoMatcherProductProps = {
  product: {
    id: string;
    name: string;
    sku: string;
    vendor: string;
    price: number;
    increments: number;
  };
};

export const PromoMatcherProduct = ({ product }: PromoMatcherProductProps) => {
  return (
    <div className="grid h-[72px] w-full grid-cols-[auto_1fr_auto] items-center gap-6 bg-white p-4">
      <div className="h-12">
        <img
          src="https://staging.services.highfive.vet/storage/vendor-images/mwi.png"
          className="h-full"
          alt={product.vendor}
        />
      </div>
      <div>
        <p className="max-w-96 text-sm font-medium text-black">
          {product.name}
        </p>
        <div className="flex items-center gap-4 divide-x-1 divide-solid divide-black/10">
          <span>
            <span className="text-xs text-black/65">SKU: </span>
            <span className="mr-3 text-xs font-medium text-black">
              {product.sku}
            </span>
          </span>
          <span className="text-xs text-black">{product.vendor}</span>
        </div>
      </div>
      <div className="flex max-w-40 items-center gap-3">
        <span className="text-sm font-medium">
          {getPriceString(product.price)}
        </span>
        <AddToCartInput
          originalAmount={product.increments}
          minIncrement={product.increments}
          onUpdate={() => {}}
        />
      </div>
    </div>
  );
};

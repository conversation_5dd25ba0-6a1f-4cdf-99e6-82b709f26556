import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';

type PromoMatcherTitleProps = {
  promoType: string;
  title: string;
};

export const PromoMatcherTitle = ({
  promoType,
  title,
}: PromoMatcherTitleProps) => {
  return (
    <div className="flex-7">
      <div className="flex items-center gap-2">
        <DiscountIcon className="h-6 w-6" />
        <span className="text-sm font-medium text-black">
          Promotion • {promoType}
        </span>
      </div>
      <h4 className="text-lg font-semibold text-gray-900">{title}</h4>
    </div>
  );
};
